#!/usr/bin/env python3
"""
<PERSON>ript to fix vector dimension mismatch by recreating the vector_embeddings table
with the correct dimensions.

Usage: cd $(dirname "$0")/.. && PYTHONPATH=. python3 scripts/fix_vector_dimensions.py
"""

import asyncio
import logging
import os
import sys
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.config import get_settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def fix_vector_dimensions():
    """Fix vector dimension mismatch by recreating the table."""
    
    settings = get_settings()
    expected_dimensions = settings.VECTOR_DIMENSIONS
    
    logger.info(f"Expected dimensions from config: {expected_dimensions}")
    
    engine = create_async_engine(settings.POSTGRES_URI)
    
    try:
        async with engine.begin() as conn:
            # Check current table structure
            result = await conn.execute(text("""
                SELECT 
                    t.typname,
                    a.atttypmod
                FROM pg_class c
                JOIN pg_attribute a ON a.attrelid = c.oid
                JOIN pg_type t ON t.oid = a.atttypid
                WHERE c.relname = 'vector_embeddings'
                AND a.attname = 'embedding'
                AND NOT a.attisdropped
            """))
            
            current_info = result.fetchone()
            if current_info:
                current_dimensions = current_info[1] - 4 if current_info[1] > 0 else None
                logger.info(f"Current table dimensions: {current_dimensions}")
                
                if current_dimensions == expected_dimensions:
                    logger.info("Dimensions already match. No changes needed.")
                    return
                
                logger.warning(f"Dimension mismatch: current={current_dimensions}, expected={expected_dimensions}")
            else:
                logger.info("vector_embeddings table not found or no embedding column")
            
            # Check if there's any data in the table
            count_result = await conn.execute(text("SELECT COUNT(*) FROM vector_embeddings"))
            row_count = count_result.scalar()
            
            if row_count > 0:
                logger.warning(f"Table contains {row_count} rows. This operation will delete all data!")
                response = input("Do you want to continue? This will delete all existing embeddings! (yes/no): ")
                if response.lower() != 'yes':
                    logger.info("Operation cancelled by user")
                    return
            
            logger.info("Recreating vector_embeddings table...")
            
            # Drop existing table and recreate with correct dimensions
            await conn.execute(text("DROP TABLE IF EXISTS vector_embeddings CASCADE"))
            logger.info("Dropped existing vector_embeddings table")
            
            # Create new table with correct dimensions
            create_table_sql = f"""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR(255) NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata TEXT,
                embedding vector({expected_dimensions}) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            await conn.execute(text(create_table_sql))
            logger.info(f"Created vector_embeddings table with vector({expected_dimensions})")
            
            # Create indexes
            await conn.execute(text("CREATE INDEX idx_document_id ON vector_embeddings(document_id);"))
            logger.info("Created document_id index")
            
            # Create vector index
            await conn.execute(text("""
                CREATE INDEX vector_idx
                ON vector_embeddings
                USING ivfflat (embedding vector_cosine_ops)
                WITH (lists = 100);
            """))
            logger.info("Created IVFFlat vector index")
            
            # Grant permissions
            try:
                await conn.execute(text("GRANT USAGE, SELECT ON SEQUENCE vector_embeddings_id_seq TO PUBLIC;"))
                logger.info("Granted sequence permissions")
            except Exception as e:
                logger.debug(f"Sequence permission grant skipped: {e}")
            
            logger.info("✅ Vector dimensions fix completed successfully!")
            logger.info(f"Table now supports {expected_dimensions}-dimensional vectors")
            
    except Exception as e:
        logger.error(f"Error fixing vector dimensions: {e}")
        raise
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(fix_vector_dimensions())
