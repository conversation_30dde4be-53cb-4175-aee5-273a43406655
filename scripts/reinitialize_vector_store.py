#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reinitialize the vector store with updated dimensions.
This will trigger the dimension check and table recreation if needed.

Usage: cd $(dirname "$0")/.. && PYTHONPATH=. python3 scripts/reinitialize_vector_store.py
"""

import asyncio
import logging
import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.config import get_settings
from core.vector_store.pgvector_store import PGVectorStore

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def reinitialize_vector_store():
    """Reinitialize the vector store to trigger dimension check and table recreation."""
    
    try:
        settings = get_settings()
        logger.info(f"Current vector dimensions setting: {settings.VECTOR_DIMENSIONS}")
        
        # Create PGVectorStore instance
        vector_store = PGVectorStore(uri=settings.POSTGRES_URI)
        
        # Initialize - this will check dimensions and recreate table if needed
        logger.info("Initializing vector store...")
        success = await vector_store.initialize()
        
        if success:
            logger.info("✅ Vector store initialization completed successfully!")
        else:
            logger.error("❌ Vector store initialization failed!")
            return False
            
        # Clean up
        await vector_store.engine.dispose()
        return True
        
    except Exception as e:
        logger.error(f"Error during vector store initialization: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(reinitialize_vector_store())
    if not success:
        sys.exit(1)
