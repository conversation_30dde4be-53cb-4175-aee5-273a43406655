# Purpose: Development Docker Compose configuration
# This file is for local development - it builds images from source code
# Use start-dev.sh to run this with automatic port detection from morphik.toml
# Note: This is NOT for production use - see docker-compose.run.yml for production

x-ollama-check: &ollama-check
  # This command reads the toml file and checks if any provider is set to "ollama"
  command: >
    /bin/sh -c '
    grep -q "provider *= *\"ollama\"" morphik.toml &&
    echo "true" > /tmp/needs_ollama ||
    echo "false" > /tmp/needs_ollama'

services:
  config-check:
    image: alpine
    volumes:
      - ./morphik.toml:/morphik.toml
    <<: *ollama-check

  morphik:
    build: .
    ports:
      # Note: Update this port mapping to match the port in morphik.toml
      - "8000:8000"
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@postgres:5432/morphik
      - PGPASSWORD=morphik
      - LOG_LEVEL=DEBUG
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
      - ./morphik.toml:/app/morphik.toml
      - huggingface_cache:/root/.cache/huggingface
      - ./core:/app/core
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      config-check:
        condition: service_completed_successfully
      ollama:
        condition: service_started
        required: false
    networks:
      - morphik-network
    env_file:
      - .env
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  worker:
    build:
      context: .
      dockerfile: dockerfile
    command: arq core.workers.ingestion_worker.WorkerSettings
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@postgres:5432/morphik
      - PGPASSWORD=morphik
      - LOG_LEVEL=DEBUG
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
      - ./morphik.toml:/app/morphik.toml
      - huggingface_cache:/root/.cache/huggingface
      - ./core:/app/core
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      config-check:
        condition: service_completed_successfully
      ollama:
        condition: service_started
        required: false
    networks:
      - morphik-network
    env_file:
      - .env
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - morphik-network

  postgres:
    image: pgvector/pgvector:pg16
    shm_size: 128mb
    environment:
      - POSTGRES_USER=morphik
      - POSTGRES_PASSWORD=morphik
      - POSTGRES_DB=morphik
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U morphik -d morphik"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - morphik-network

  ollama:
    image: ollama/ollama:latest
    profiles:
      - ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
      - ./ollama-entrypoint.sh:/ollama-entrypoint.sh
    networks:
      - morphik-network
    entrypoint: ["/ollama-entrypoint.sh"]

networks:
  morphik-network:
    driver: bridge

volumes:
  postgres_data:
  ollama_data:
  huggingface_cache:
  redis_data:
